import json
import re

# 读取刚才修改的文件
with open('医院院长驾驶舱-TEMPLATE.DET2', 'r', encoding='utf-8') as f:
    data = json.loads(f.read())

# 解析componentData
comp_data = json.loads(data['componentData'])

print('进行高级样式优化...')

# 院长驾驶舱专业配色和样式
EXECUTIVE_THEME = {
    'colors': {
        'primary': '#2E86AB',      # 专业蓝
        'secondary': '#A23B72',    # 深紫红
        'success': '#F18F01',      # 橙色
        'info': '#C73E1D',         # 深红
        'light': '#F5F5F5',        # 浅灰
        'dark': '#2C3E50'          # 深蓝灰
    },
    'gradients': {
        'primary': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'secondary': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'success': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'info': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
    }
}

# 为不同类型的组件应用专业样式
for i, comp in enumerate(comp_data):
    comp_type = comp.get('component', '')
    comp_name = comp.get('name', '')
    inner_type = comp.get('innerType', '')
    
    style = comp.get('style', {})
    prop_value = comp.get('propValue', {})
    
    # 指标卡样式优化
    if '指标卡' in comp_name:
        # 现代化卡片样式
        style.update({
            'borderRadius': 12,
            'boxShadow': '0 8px 32px rgba(0,0,0,0.12)',
            'border': '1px solid rgba(255,255,255,0.2)',
            'backdropFilter': 'blur(10px)',
            'background': 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))'
        })
        
        # 根据位置设置不同的强调色
        left = style.get('left', 0)
        if left < 400:  # 左侧指标
            style['borderTop'] = f'3px solid {EXECUTIVE_THEME["colors"]["primary"]}'
        elif left < 800:  # 中间指标
            style['borderTop'] = f'3px solid {EXECUTIVE_THEME["colors"]["secondary"]}'
        else:  # 右侧指标
            style['borderTop'] = f'3px solid {EXECUTIVE_THEME["colors"]["success"]}'
    
    # 图表组件样式优化
    elif inner_type in ['line', 'bar', 'pie', 'table']:
        style.update({
            'borderRadius': 8,
            'boxShadow': '0 4px 20px rgba(0,0,0,0.08)',
            'border': '1px solid rgba(46, 134, 171, 0.2)',
            'background': 'rgba(255,255,255,0.95)'
        })
        
        # 为图表添加专业的标题样式
        if 'title' in prop_value:
            # 这里可以进一步修改图表的标题样式
            pass
    
    # 富文本组件优化
    elif inner_type == 'rich-text':
        text_value = prop_value.get('textValue', '')
        
        # 优化主标题
        if '医院院长驾驶舱' in text_value:
            new_text = '''<div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; margin-bottom: 10px;">
                <h1 style="color: white; font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: 700; margin: 0; text-shadow: 2px 2px 8px rgba(0,0,0,0.3); letter-spacing: 2px;">
                    🏥 医院院长驾驶舱
                </h1>
                <p style="color: rgba(255,255,255,0.9); font-size: 16px; margin: 10px 0 0 0; font-weight: 300;">
                    Hospital CEO Dashboard - 实时运营监控
                </p>
            </div>'''
            prop_value['textValue'] = new_text
            
            # 调整容器样式
            style.update({
                'borderRadius': 12,
                'overflow': 'hidden'
            })
        
        # 优化其他文本标签
        elif any(keyword in text_value for keyword in ['指标', '概览', '分析']):
            # 为区域标题添加样式
            if '核心运营指标' in text_value:
                new_text = '''<div style="background: linear-gradient(90deg, #2E86AB, #A23B72); padding: 12px 20px; border-radius: 8px;">
                    <h3 style="color: white; margin: 0; font-family: 'Microsoft YaHei'; font-size: 18px; font-weight: 600;">
                        📊 核心运营指标
                    </h3>
                </div>'''
                prop_value['textValue'] = new_text

# 优化画布样式
canvas_style = data.get('canvasStyleData', {})
if isinstance(canvas_style, str):
    canvas_style = json.loads(canvas_style)

# 设置专业的背景
canvas_style.update({
    'backgroundColor': '#f8f9fa',
    'backgroundImage': 'radial-gradient(circle at 25% 25%, #667eea 0%, transparent 50%), radial-gradient(circle at 75% 75%, #764ba2 0%, transparent 50%)',
    'backgroundSize': '100% 100%'
})

# 更新数据
data['canvasStyleData'] = json.dumps(canvas_style) if isinstance(data['canvasStyleData'], str) else canvas_style
data['componentData'] = json.dumps(comp_data)

# 保存最终版本
with open('医院院长驾驶舱-专业版-TEMPLATE.DET2', 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, separators=(',', ':'))

print('高级样式优化完成！')
print('专业版文件已保存为: 医院院长驾驶舱-专业版-TEMPLATE.DET2')
print('\n高级优化内容:')
print('✅ 玻璃态效果 - 现代化半透明卡片')
print('✅ 渐变背景 - 专业的径向渐变')
print('✅ 分层设计 - 不同重要级别的视觉层次')
print('✅ 图标装饰 - 添加医院和仪表板图标')
print('✅ 英文副标题 - 国际化专业感')
print('✅ 色彩编码 - 按功能区域的颜色区分')
print('✅ 阴影深度 - 增强立体感和层次感')
