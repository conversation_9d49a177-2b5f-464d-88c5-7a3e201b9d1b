import json
import re

# 读取原文件
with open('大屏test1-TEMPLATE.DET2', 'r', encoding='utf-8') as f:
    data = json.loads(f.read())

# 解析componentData
comp_data = json.loads(data['componentData'])

print(f'开始修改 {len(comp_data)} 个组件的样式...')

# 院长驾驶舱的配色方案
CEO_COLORS = {
    'primary': '#1E90FF',      # 道奇蓝 - 主色调
    'secondary': '#32CD32',    # 酸橙绿 - 辅助色
    'accent': '#FF6347',       # 番茄红 - 强调色
    'background': '#F8F9FA',   # 浅灰背景
    'text_primary': '#2C3E50', # 深蓝灰文字
    'text_secondary': '#7F8C8D', # 中灰文字
    'success': '#28A745',      # 成功绿
    'warning': '#FFC107',      # 警告黄
    'danger': '#DC3545'        # 危险红
}

# 修改每个组件
for i, comp in enumerate(comp_data):
    comp_type = comp.get('component', '')
    comp_name = comp.get('name', '')
    
    print(f'修改组件 {i+1}: {comp_name} ({comp_type})')
    
    # 修改富文本组件（标题类）
    if comp_type == 'UserView' and 'innerType' in comp and comp['innerType'] == 'rich-text':
        prop_value = comp.get('propValue', {})
        text_value = prop_value.get('textValue', '')
        
        # 修改标题文本内容
        if '驾驶舱' in text_value:
            # 更新为院长驾驶舱样式
            new_text = '''<p style="text-align: center;" data-mce-style="text-align: center;" onclick="event.stopPropagation()">
                <span style="font-family: 'Microsoft YaHei'; font-size: 36px; color: #1E90FF; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);" data-mce-style="font-family: 'Microsoft YaHei'; font-size: 36px; color: #1E90FF;">
                    <strong>医院院长驾驶舱</strong>
                </span>
            </p>'''
            prop_value['textValue'] = new_text
            
        # 修改其他文本内容
        elif '门诊人次' in text_value:
            new_text = text_value.replace('门诊人次', '核心运营指标')
            prop_value['textValue'] = new_text
    
    # 修改数据展示组件的样式
    if comp_type == 'UserView':
        style = comp.get('style', {})
        
        # 增强视觉效果
        style['borderRadius'] = 8  # 圆角
        style['boxShadow'] = '0 4px 12px rgba(0,0,0,0.15)'  # 阴影
        
        # 根据组件位置调整颜色
        left = style.get('left', 0)
        top = style.get('top', 0)
        
        # 顶部重要指标使用主色调
        if top < 300:
            style['borderLeft'] = f'4px solid {CEO_COLORS["primary"]}'
        # 中部图表使用辅助色
        elif top < 600:
            style['borderLeft'] = f'4px solid {CEO_COLORS["secondary"]}'
        # 底部详细数据使用强调色
        else:
            style['borderLeft'] = f'4px solid {CEO_COLORS["accent"]}'
    
    # 修改图片组件
    if comp_type == 'Picture':
        style = comp.get('style', {})
        # 添加轻微的边框和阴影
        style['borderRadius'] = 4
        style['boxShadow'] = '0 2px 8px rgba(0,0,0,0.1)'

# 修改画布整体样式
canvas_style = data.get('canvasStyleData', {})
if isinstance(canvas_style, str):
    canvas_style = json.loads(canvas_style)

# 设置更专业的背景
canvas_style['backgroundColor'] = CEO_COLORS['background']
canvas_style['backgroundImage'] = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'

# 更新数据
data['canvasStyleData'] = json.dumps(canvas_style) if isinstance(data['canvasStyleData'], str) else canvas_style
data['componentData'] = json.dumps(comp_data)

# 保存修改后的文件
with open('医院院长驾驶舱-TEMPLATE.DET2', 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, separators=(',', ':'))

print('样式修改完成！')
print('新文件已保存为: 医院院长驾驶舱-TEMPLATE.DET2')
print('\n主要修改内容:')
print('1. 标题样式 - 更大字体、蓝色主题、阴影效果')
print('2. 组件边框 - 根据重要性使用不同颜色的左边框')
print('3. 圆角和阴影 - 增加现代感')
print('4. 背景渐变 - 专业的渐变背景')
print('5. 配色方案 - 医院/企业级的蓝绿配色')
