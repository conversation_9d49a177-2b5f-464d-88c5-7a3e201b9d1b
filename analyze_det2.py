import json

# 读取文件
with open('大屏test1-TEMPLATE.DET2', 'r', encoding='utf-8') as f:
    data = json.loads(f.read())

# 先查看数据结构
print('文件主要结构:')
for key in data.keys():
    if key == 'snapshot':
        print(f'{key}: [图片数据]')
    elif key == 'componentData':
        comp_data = data[key]
        print(f'{key}: {type(comp_data)} - 长度: {len(comp_data) if isinstance(comp_data, (list, str)) else "N/A"}')
        if isinstance(comp_data, list) and len(comp_data) > 0:
            print(f'  第一个元素类型: {type(comp_data[0])}')
        elif isinstance(comp_data, str):
            print(f'  前100字符: {comp_data[:100]}')
    else:
        value = str(data[key])
        print(f'{key}: {value[:100]}...' if len(value) > 100 else f'{key}: {value}')

# 如果componentData是字符串，尝试解析
comp_data = data.get('componentData', [])
if isinstance(comp_data, str):
    try:
        comp_data = json.loads(comp_data)
        print(f'\n解析后的componentData类型: {type(comp_data)}')
    except:
        print('\ncomponentData无法解析为JSON')

if isinstance(comp_data, list):
    print(f'\n组件数量: {len(comp_data)}')
    for i, comp in enumerate(comp_data[:3]):  # 只看前3个
        print(f'\n组件 {i+1}:')
        print(f'  类型: {type(comp)}')
        if isinstance(comp, dict):
            for key in comp.keys():
                value = str(comp[key])
                print(f'  {key}: {value[:50]}...' if len(value) > 50 else f'  {key}: {value}')
        else:
            print(f'  内容: {str(comp)[:100]}')
        if i >= 2:
            break
