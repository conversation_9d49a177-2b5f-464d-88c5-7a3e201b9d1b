<div data-v-2eb34a22="" id="editor-canvas-main" class="editor edit canvas-area-shadow editor-main"
    style="font-size: 14px; color: rgb(255, 255, 255); background-color: rgba(206, 224, 217, 0.55); font-family: PingFang !important; width: 1440px; height: 810px;">
    <div class="canvas-drag-tip">按住空格可拖动画布</div><!----><!---->
    <div data-v-ad79b399="" data-v-2eb34a22="" class="pop-area"
        style="height: 0px !important; overflow: hidden; border: 0px !important;">
        <div data-v-ad79b399="" style="width: 100%; height: 100%;">
            <div data-v-ad79b399="" class="pop-area-main" style="font-size: 22.5px; height: 121.5px;"><span
                    data-v-ad79b399="">可点击或拖拽查询组件到此位置，点击预览可查看弹窗区</span></div>
        </div>
    </div><!----><!----><!----><!----><!---->
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-7160120621324570624"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="Picture" component-id="7160120621324570624"
        style="padding: 0px !important; width: 1436.36px; height: 69.3182px; left: 0.201334px; top: 5.471px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer active shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990=""
                class="bar-main bar-main-right bar-main-background edit-bar edit-bar-active">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-7160120621324570624"
                style="padding: 0px; border-radius: 0px; overflow: hidden;"><!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-690b1601="" data-v-2eb34a22="" class="pic-main component"
                        id="component7160120621324570624" scale="0.75" is-edit="true"
                        canvas-style-data="[object Object]" canvas-view-info="[object Object]" dv-info="[object Object]"
                        active="true" canvas-active="true" show-position="edit" font-family="PingFang"
                        style="opacity: 1;"><img data-v-690b1601="" draggable="false"
                            src="./de2api/static-resource/7160120637921431552.png"
                            style="position: relative; width: 100%; height: 100%;"></div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-point"
                style="margin-left: -4px; margin-top: -4px; left: 0px; top: 0px; cursor: nw-resize;"></div>
            <div data-v-2e205990="" class="shape-point"
                style="margin-left: -4px; margin-top: -4px; left: 718.182px; top: 0px; cursor: n-resize;"></div>
            <div data-v-2e205990="" class="shape-point"
                style="margin-left: -4px; margin-top: -4px; left: 1436.36px; top: 0px; cursor: ne-resize;"></div>
            <div data-v-2e205990="" class="shape-point"
                style="margin-left: -4px; margin-top: -4px; left: 1436.36px; top: 34px; cursor: e-resize;"></div>
            <div data-v-2e205990="" class="shape-point"
                style="margin-left: -4px; margin-top: -4px; left: 1436.36px; top: 69.3182px; cursor: se-resize;"></div>
            <div data-v-2e205990="" class="shape-point"
                style="margin-left: -4px; margin-top: -4px; left: 718.182px; top: 69.3182px; cursor: s-resize;"></div>
            <div data-v-2e205990="" class="shape-point"
                style="margin-left: -4px; margin-top: -4px; left: 0px; top: 69.3182px; cursor: sw-resize;"></div>
            <div data-v-2e205990="" class="shape-point"
                style="margin-left: -4px; margin-top: -4px; left: 0px; top: 34px; cursor: w-resize;"></div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047919456256"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047919456256"
        style="padding: 0px !important; width: 427.941px; height: 52.9412px; left: 508.145px; top: 9.10633px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047919456256"
                style="padding: 9px; border-radius: 0px;"><!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047919456256" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="zoom: 0.75; opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px; display: none;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-465107a4="" data-v-f9791064="" class="rich-main-class"
                                    style="--de-canvas-scale: 0.75;"><!---->
                                    <div data-v-465107a4="" id="tinymce-view-1139619047919456256-common-canvas"
                                        class="custom-text-content mce-content-body mce-content-readonly"
                                        spellcheck="false" contenteditable="false">
                                        <p style="text-align: center;" data-mce-style="text-align: center;"
                                            onclick="event.stopPropagation()"><span
                                                style="font-family: 'Microsoft YaHei'; font-size: 32px;"
                                                data-mce-style="font-family: 'Microsoft YaHei'; font-size: 32px;"><strong><span
                                                        style="color: #000000;"
                                                        data-mce-style="color: #000000;">医院院长驾驶舱</span></strong></span></p>
                                    </div><!---->
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-7160479732851544064"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="Picture" component-id="7160479732851544064"
        style="padding: 0px !important; width: 29.7794px; height: 33.0882px; left: 46.3235px; top: 17.6471px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-7160479732851544064"
                style="padding: 0px; border-radius: 0px; overflow: hidden;"><!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-690b1601="" data-v-2eb34a22="" class="pic-main component"
                        id="component7160479732851544064" scale="0.75" is-edit="true"
                        canvas-style-data="[object Object]" canvas-view-info="[object Object]" dv-info="[object Object]"
                        active="false" canvas-active="true" show-position="edit" font-family="PingFang"
                        style="opacity: 1;"><img data-v-690b1601="" draggable="false"
                            src="./de2api/static-resource/7160479758864617472.png"
                            style="position: relative; width: 100%; height: 100%;"></div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047638437888"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047638437888"
        style="padding: 0px !important; width: 134px; height: 54px; left: 6.75524px; top: 106.221px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047638437888"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047638437888" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-367"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(30, 144, 255); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">1,256</span><!---->
                                    </div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(144, 238, 144); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">今日门诊人次</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047722323968"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047722323968"
        style="padding: 0px !important; width: 133.456px; height: 89.3382px; left: 7.72059px; top: 166.544px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047722323968"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047722323968" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-375"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(14, 114, 213); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">2,856.8</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(14, 114, 213); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            万</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">今年总收入</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047797821440"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047797821440"
        style="padding: 0px !important; width: 133px; height: 53px; left: 146.273px; top: 107.221px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047797821440"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047797821440" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-386"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(30, 144, 255); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">892</span><!---->
                                    </div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(144, 238, 144); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">今日住院人次</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619048368246784"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619048368246784"
        style="padding: 0px !important; width: 133.456px; height: 89.3382px; left: 146.691px; top: 166.544px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619048368246784"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619048368246784" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-394"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(50, 73, 132); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">2,456.3</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(50, 73, 132); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            万</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">去年总收入</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619048280166400"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619048280166400"
        style="padding: 0px !important; width: 137px; height: 51px; left: 284.853px; top: 109.564px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619048280166400"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619048280166400" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-402"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(4, 179, 94); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">16.3</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(4, 179, 94); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            %</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">收入增长率</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047755878400"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047755878400"
        style="padding: 0px !important; width: 133.784px; height: 89.1892px; left: 285.811px; top: 166.544px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047755878400"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047755878400" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-410"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(4, 179, 94); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">95.2</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(4, 179, 94); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            %</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">床位使用率</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047554551808"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047554551808"
        style="padding: 0px !important; width: 133.846px; height: 88.8462px; left: 1016.3px; top: 71.5385px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047554551808"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047554551808" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-418"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(14, 114, 213); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">238.5</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(14, 114, 213); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            万</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">本月收入</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047328059392"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047328059392"
        style="padding: 0px !important; width: 133.846px; height: 88.8462px; left: 1016.54px; top: 166.154px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047328059392"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047328059392" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-426"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(14, 114, 213); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">1,856</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(14, 114, 213); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            人</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">在院患者</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047386779648"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047386779648"
        style="padding: 0px !important; width: 133.846px; height: 88.8462px; left: 1154.19px; top: 74px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047386779648"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047386779648" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-434"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(50, 73, 132); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">205.3</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(50, 73, 132); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            万</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">上月收入</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047437111296"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047437111296"
        style="padding: 0px !important; width: 133.846px; height: 88.8462px; left: 1155.43px; top: 166.304px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047437111296"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047437111296" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-442"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(50, 73, 132); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">1,623</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(50, 73, 132); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            人</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">医护人员</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047504220160"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047504220160"
        style="padding: 0px !important; width: 133.846px; height: 88.8462px; left: 1294.62px; top: 71.5385px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047504220160"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047504220160" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-450"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(4, 179, 94); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">7.2</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(4, 179, 94); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            天</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">平均住院日</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619048439549952"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619048439549952"
        style="padding: 0px !important; width: 133.846px; height: 88.8462px; left: 1294.57px; top: 166.304px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619048439549952"
                style="padding: 0px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160531731596972032.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619048439549952" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none; position: absolute; border: 1px solid rgba(0, 0, 0, 0.01); background-color: rgba(0, 0, 0, 0.01); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-2b9e1a2f="" data-v-f9791064="" class="" themes="dark"
                                    style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                                    <div data-v-3542a5cb="" data-v-2b9e1a2f="" class="track-bar"
                                        style="position: absolute; left: 50%; top: 50%;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-458"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-2b9e1a2f=""><span data-v-2b9e1a2f=""
                                            style="color: rgb(4, 179, 94); font-size: 21px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">98.5</span><span
                                            data-v-2b9e1a2f=""
                                            style="color: rgb(4, 179, 94); font-size: 15px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">
                                            %</span></div>
                                    <div data-v-2b9e1a2f="" style="margin-top: 0px;"><span data-v-2b9e1a2f=""
                                            style="color: rgb(10, 22, 50); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;; font-weight: bold; font-style: normal; letter-spacing: 0px; text-shadow: none; font-synthesis: weight style;">患者满意度</span>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047873318912"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047873318912"
        style="padding: 0px !important; width: 583.125px; height: 450.938px; left: 426.562px; top: 72.1875px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047873318912"
                style="padding: 9px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160541545785987072.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047873318912" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: center; margin-bottom: 9px;">
                                <p data-v-f9791064=""
                                    style="font-size: 13.5px; color: rgb(48, 59, 85); text-align: center; font-style: normal; font-weight: bold; text-shadow: none; letter-spacing: 0px; font-synthesis: weight style; width: fit-content; max-width: 100%; word-break: break-word; white-space: pre-wrap; font-family: &quot;Microsoft YaHei&quot;;"
                                    onclick="event.stopPropagation()">科室收入排名</p>
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none;"><!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-e38cf473="" class="canvas-area">
                                    <div data-v-3542a5cb="" data-v-e38cf473="" class="track-bar"
                                        style="position: absolute; left: 50px; top: 50px;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-469"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-e38cf473="" class="canvas-content">
                                        <div data-v-e38cf473="" id="container-canvas-1139619047873318912-common"
                                            style="position: relative; height: 100%;"><canvas width="1130" height="816"
                                                style="width: 565.125px; height: 408px; display: block; cursor: default;"></canvas>
                                            <div class="antv-s2-tooltip-container antv-s2-tooltip-container-hide"
                                                style="background: rgb(90, 92, 98); font-size: 9px; font-family: PingFang; color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 8px 0px; border-radius: 3px; padding: 4px 12px; opacity: 0.95; position: absolute; left: 485.125px; top: 126.288px; pointer-events: none;">
                                                2</div>
                                        </div>
                                    </div><!----><!---->
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div>
            <div data-v-2e205990="" class="de-drag-area de-drag-top"></div>
            <div data-v-2e205990="" class="de-drag-area de-drag-right"></div>
            <div data-v-2e205990="" class="de-drag-area de-drag-bottom"></div>
            <div data-v-2e205990="" class="de-drag-area de-drag-left"></div>
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619048171114496"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619048171114496"
        style="padding: 0px !important; width: 411.458px; height: 260.417px; left: 7.29167px; top: 261.458px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619048171114496"
                style="padding: 9px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160543437228347392.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619048171114496" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: center; margin-bottom: 9px;">
                                <p data-v-f9791064=""
                                    style="font-size: 13.5px; color: rgb(48, 59, 85); text-align: center; font-style: normal; font-weight: bold; text-shadow: none; letter-spacing: 0px; font-synthesis: weight style; width: fit-content; max-width: 100%; word-break: break-word; white-space: pre-wrap; font-family: &quot;Microsoft YaHei&quot;;"
                                    onclick="event.stopPropagation()">月度收入趋势</p>
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none;"><!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-af854cd1="" data-v-f9791064="" class="canvas-area" dynamic-area-id="">
                                    <div data-v-3542a5cb="" data-v-af854cd1="" class="track-bar"
                                        style="position: absolute; left: 50px; top: 50px;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-479"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-af854cd1="" class="canvas-content"
                                        id="container-canvas-1139619048171114496-common" data-chart-source-type="G2Plot"
                                        size-sensor-id="2">
                                        <div id="container-canvas-1139619048171114496-common_point_1139619048171114496_"
                                            style="position: fixed; z-index: 1; opacity: 0.95; transition: opacity 0.2s ease-in-out; overflow: visible;">
                                        </div>
                                        <div style="position:relative;"><canvas width="786" height="436"
                                                style="width: 393px; height: 218px; display: inline-block; vertical-align: middle;"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619048410189824"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619048410189824"
        style="padding: 0px !important; width: 408.696px; height: 259.783px; left: 1016.3px; top: 261.957px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619048410189824"
                style="padding: 9px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160543437228347392.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619048410189824" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: center; margin-bottom: 9px;">
                                <p data-v-f9791064=""
                                    style="font-size: 13.5px; color: rgb(48, 59, 85); text-align: center; font-style: normal; font-weight: bold; text-shadow: none; letter-spacing: 0px; font-synthesis: weight style; width: fit-content; max-width: 100%; word-break: break-word; white-space: pre-wrap; font-family: &quot;Microsoft YaHei&quot;;"
                                    onclick="event.stopPropagation()">床位使用率趋势</p>
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none;"><!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-af854cd1="" data-v-f9791064="" class="canvas-area" dynamic-area-id="">
                                    <div data-v-3542a5cb="" data-v-af854cd1="" class="track-bar"
                                        style="position: absolute; left: 50px; top: 50px;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-487"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-af854cd1="" class="canvas-content"
                                        id="container-canvas-1139619048410189824-common" data-chart-source-type="G2Plot"
                                        size-sensor-id="1">
                                        <div id="container-canvas-1139619048410189824-common_point_1139619048410189824_"
                                            style="position: fixed; z-index: 1; opacity: 0.95; transition: opacity 0.2s ease-in-out; overflow: visible;">
                                        </div>
                                        <div style="position:relative;"><canvas width="782" height="434"
                                                style="width: 391px; height: 217px; display: inline-block; vertical-align: middle; cursor: default;"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619048204668928"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619048204668928"
        style="padding: 0px !important; width: 409.615px; height: 260.769px; left: 1016.3px; top: 529.615px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619048204668928"
                style="padding: 9px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160543437228347392.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619048204668928" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: center; margin-bottom: 9px;">
                                <p data-v-f9791064=""
                                    style="font-size: 13.5px; color: rgb(48, 59, 85); text-align: center; font-style: normal; font-weight: bold; text-shadow: none; letter-spacing: 0px; font-synthesis: weight style; width: fit-content; max-width: 100%; word-break: break-word; white-space: pre-wrap; font-family: &quot;Microsoft YaHei&quot;;"
                                    onclick="event.stopPropagation()">患者满意度趋势</p>
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none;"><!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-af854cd1="" data-v-f9791064="" class="canvas-area" dynamic-area-id="">
                                    <div data-v-3542a5cb="" data-v-af854cd1="" class="track-bar"
                                        style="position: absolute; left: 50px; top: 50px;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-495"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-af854cd1="" class="canvas-content"
                                        id="container-canvas-1139619048204668928-common" data-chart-source-type="G2Plot"
                                        size-sensor-id="3">
                                        <div id="container-canvas-1139619048204668928-common_point_1139619048204668928_"
                                            style="position: fixed; z-index: 1; opacity: 0.95; transition: opacity 0.2s ease-in-out; overflow: visible;">
                                        </div>
                                        <div style="position:relative;"><canvas width="784" height="436"
                                                style="width: 392px; height: 218px; display: inline-block; vertical-align: middle;"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619048317915136"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619048317915136"
        style="padding: 0px !important; width: 582.609px; height: 263.077px; left: 427.079px; top: 528.969px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619048317915136"
                style="padding: 9px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160557129043021824.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619048317915136" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: center; margin-bottom: 9px;">
                                <p data-v-f9791064=""
                                    style="font-size: 13.5px; color: rgb(48, 59, 85); text-align: center; font-style: normal; font-weight: bold; text-shadow: none; letter-spacing: 0px; font-synthesis: weight style; width: fit-content; max-width: 100%; word-break: break-word; white-space: pre-wrap; font-family: &quot;Microsoft YaHei&quot;;"
                                    onclick="event.stopPropagation()">医院运营指标</p>
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none;"><!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-af854cd1="" data-v-f9791064="" class="canvas-area" dynamic-area-id="">
                                    <div data-v-3542a5cb="" data-v-af854cd1="" class="track-bar"
                                        style="position: absolute; left: 50px; top: 50px;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-503"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-af854cd1="" class="canvas-content"
                                        id="container-canvas-1139619048317915136-common" data-chart-source-type="G2Plot"
                                        size-sensor-id="4">
                                        <div style="position:relative;"><canvas width="1130" height="442"
                                                style="width: 565px; height: 221px; display: inline-block; vertical-align: middle;"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-1139619047684575232"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="1139619047684575232"
        style="padding: 0px !important; width: 411.364px; height: 262.5px; left: 7.95455px; top: 529.545px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-1139619047684575232"
                style="padding: 9px; border-radius: 5px; background: url(&quot;./de2api/static-resource/7160557129043021824.png&quot;) no-repeat rgb(255, 255, 255);">
                <!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component1139619047684575232" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="opacity: 1;">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: center; margin-bottom: 9px;">
                                <p data-v-f9791064=""
                                    style="font-size: 13.5px; color: rgb(48, 59, 85); text-align: center; font-style: normal; font-weight: bold; text-shadow: none; letter-spacing: 0px; font-synthesis: weight style; width: fit-content; max-width: 100%; word-break: break-word; white-space: pre-wrap; font-family: &quot;Microsoft YaHei&quot;;"
                                    onclick="event.stopPropagation()">科室效益分析</p>
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); display: none;"><!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-af854cd1="" data-v-f9791064="" class="canvas-area" dynamic-area-id="">
                                    <div data-v-3542a5cb="" data-v-af854cd1="" class="track-bar"
                                        style="position: absolute; left: 50px; top: 50px;">
                                        <div data-v-3542a5cb="" class="ed-dropdown"><input data-v-3542a5cb=""
                                                id="view-track-bar-null" type="button" hidden="" role="button"
                                                tabindex="0" class="ed-tooltip__trigger" aria-controls="ed-id-8050-511"
                                                aria-expanded="false" aria-haspopup="menu"><!--v-if--><!--v-if--></div>
                                    </div>
                                    <div data-v-af854cd1="" class="canvas-content"
                                        id="container-canvas-1139619047684575232-common" data-chart-source-type="G2Plot"
                                        size-sensor-id="5">
                                        <div style="position:relative;"><canvas width="786" height="440"
                                                style="width: 393px; height: 220px; display: inline-block; vertical-align: middle;"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-7348625339578322944"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="Picture" component-id="7348625339578322944"
        style="padding: 0px !important; width: 87.9808px; height: 43.2692px; left: 34.6154px; top: 18.4955px;">
        <!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-7348625339578322944"
                style="padding: 0px; border-radius: 0px; overflow: hidden;"><!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-690b1601="" data-v-2eb34a22="" class="pic-main component"
                        id="component7348625339578322944" scale="0.75" is-edit="true"
                        canvas-style-data="[object Object]" canvas-view-info="[object Object]" dv-info="[object Object]"
                        active="false" canvas-active="true" show-position="edit" font-family="PingFang"
                        style="opacity: 1;"><img data-v-690b1601="" draggable="false"
                            src="./de2api/static-resource/7348625352899432448.jpg"
                            style="position: relative; width: 100%; height: 100%;"></div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-2e205990="" data-v-2eb34a22="" class="shape" id="shape-id-7348639392023449600"
        element-loading-text="导出中..." element-loading-background="rgba(255, 255, 255, 1)" tab-is-check="true"
        component-type="UserView" component-id="7348639392023449600"
        style="padding: 0px !important; width: 414px; height: 28px; left: 5.54545px; top: 74px;"><!----><!----><!---->
        <div data-v-2e205990="" class="shape-outer shape-edit">
            <div data-v-f63c32dd="" data-v-2e205990="" class="bar-main bar-main-right bar-main-background edit-bar">
                <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div>
            <div data-v-2e205990="" class="shape-inner" id="enlarge-inner-shape-7348639392023449600"
                style="padding: 9px; border-radius: 0px; background-color: rgb(255, 255, 255);"><!---->
                <div data-v-2e205990="" class="component-slot">
                    <div data-v-8a523810="" data-v-2eb34a22="" class="bash-shape component"
                        id="component7348639392023449600" is-edit="true" dv-info="[object Object]" canvas-active="true"
                        style="zoom: 0.75; opacity: 1; border-width: 0px; border-radius: 5px; border-style: solid; border-color: rgb(204, 204, 204);">
                        <div data-v-f9791064="" data-v-8a523810="" class="chart-area report-load report-load-finish"
                            element-loading-background="rgba(0,0,0,0)">
                            <div data-v-f9791064="" class="title-container"
                                style="justify-content: flex-start; margin-bottom: 0px;"><!---->
                                <div data-v-f9791064="" class="icons-container"
                                    style="color: rgb(166, 166, 166); position: absolute; border: 1px solid rgba(255, 255, 255, 0.01); background-color: rgb(255, 255, 255); border-radius: 2px; padding: 0px 2px; z-index: 1; top: 2px; left: 2px; display: none;">
                                    <!----><!----><!----><!----></div>
                            </div>
                            <div data-v-f9791064="" style="flex: 1 1 0%; overflow: hidden;">
                                <div data-v-465107a4="" data-v-f9791064="" class="rich-main-class"
                                    style="--de-canvas-scale: 0.75;"><!---->
                                    <div data-v-465107a4="" id="tinymce-view-7348639392023449600-common-canvas"
                                        class="custom-text-content mce-content-body mce-content-readonly"
                                        spellcheck="false" contenteditable="false">
                                        <p style="text-align: center;" data-mce-style="text-align: center;"
                                            onclick="event.stopPropagation()"><span style="color: #000000;"
                                                data-mce-style="color: #000000;">核心指标概览</span></p>
                                    </div><!---->
                                </div>
                            </div><!----><!----><!---->
                        </div>
                    </div>
                </div><!---->
            </div>
            <div data-v-2e205990="" class="shape-shadow" style="display: none;"></div><!---->
        </div><!---->
    </div>
    <div data-v-cc4f9e43="" data-v-2eb34a22="" class="context-menu-base context-menu-details contextmenu"
        style="top: 149.8px; left: 1261.4px; display: none;">
        <ul>
            <li style="display: none;">取消组合</li>
            <div class="ed-divider ed-divider--horizontal custom-divider" role="separator"
                style="--ed-border-style: solid; display: none;"><!--v-if--></div><!----><!---->
            <li>上移一层</li>
            <li>下移一层</li>
            <li>置于顶层</li>
            <li>置于底层</li><!---->
            <li style="display: none;">移动到大屏弹窗区</li>
            <div class="ed-divider ed-divider--horizontal custom-divider" role="separator"
                style="--ed-border-style: solid;"><!--v-if--></div>
            <li>隐藏</li>
            <li style="display: none;">取消隐藏</li>
            <li>锁定</li><!---->
            <div class="ed-divider ed-divider--horizontal custom-divider" role="separator"
                style="--ed-border-style: solid;"><!--v-if--></div><!---->
            <li>复制</li>
            <li>粘贴</li>
            <li>剪切</li>
            <div class="ed-divider ed-divider--horizontal custom-divider" role="separator"
                style="--ed-border-style: solid;"><!--v-if--></div>
            <li>删除</li><!---->
        </ul>
    </div>
    <div data-v-415f4511="" data-v-2eb34a22="" class="mark-line" id="canvas-mark-line">
        <div data-v-415f4511="" class="line xLine" style="top: 74px; display: none;"></div>
        <div data-v-415f4511="" class="line xLine" style="display: none; top: 115.962px;"></div>
        <div data-v-415f4511="" class="line xLine" style="top: 74.7892px; display: none;"></div>
        <div data-v-415f4511="" class="line yLine" style="left: 1155.43px; display: none;"></div>
        <div data-v-415f4511="" class="line yLine" style="left: 1221.11px; display: none;"></div>
        <div data-v-415f4511="" class="line yLine" style="left: 1289.28px; display: none;"></div>
    </div>
    <div data-v-869b17df="" data-v-2eb34a22="" class="area"
        style="left: 0px; top: 0px; width: 0px; height: 0px; display: none;"></div>
    <div class="ed-overlay" style="z-index: 2091; display: none;">
        <div role="dialog" aria-modal="true" aria-label="计算参数输入" aria-describedby="ed-id-8050-527"
            class="ed-overlay-dialog"></div>
    </div>
</div>